-- SQL script to add PWD-related columns to register_client and register_genius tables
-- Run these commands in your phpMyAdmin SQL tab or MySQL client

-- Add PWD-related columns to register_genius table
ALTER TABLE register_genius
ADD COLUMN is_pwd TINYINT(1) DEFAULT 0 COMMENT 'Whether user is a Person with Disability (0=No, 1=Yes)',
ADD COLUMN pwd_condition TEXT DEFAULT NULL COMMENT 'Description of PWD condition (internal use only)',
ADD COLUMN pwd_proof LONGBLOB DEFAULT NULL COMMENT 'PWD proof document (binary data)',
ADD COLUMN commission_rate DECIMAL(5,2) DEFAULT 10.00 COMMENT 'Commission percentage (default 10.00%)',
ADD COLUMN pwd_approval_status ENUM('not_applicable', 'pending', 'approved', 'rejected') DEFAULT 'not_applicable' COMMENT 'PWD approval status',
ADD COLUMN pwd_approved_by INT DEFAULT NULL COMMENT 'Admin ID who approved PWD status',
ADD COLUMN pwd_approved_at TIMESTAMP NULL DEFAULT NULL COMMENT 'When PWD status was approved',
ADD COLUMN pwd_rejection_reason TEXT DEFAULT NULL COMMENT 'Reason for PWD rejection (if applicable)';

-- Update existing records in register_genius table
UPDATE register_genius
SET
    is_pwd = 0,
    commission_rate = 10.00,
    pwd_approval_status = 'not_applicable'
WHERE
    is_pwd IS NULL
    OR commission_rate IS NULL
    OR pwd_approval_status IS NULL;

-- Add PWD-related columns to register_client table
ALTER TABLE register_client 
ADD COLUMN is_pwd TINYINT(1) DEFAULT 0 COMMENT 'Whether user is a Person with Disability (0=No, 1=Yes)',
ADD COLUMN pwd_condition TEXT DEFAULT NULL COMMENT 'Description of PWD condition (internal use only)',
ADD COLUMN pwd_proof LONGBLOB DEFAULT NULL COMMENT 'PWD proof document (binary data)',
ADD COLUMN commission_rate DECIMAL(5,2) DEFAULT 10.00 COMMENT 'Commission percentage (default 10.00%)',
ADD COLUMN pwd_approval_status ENUM('not_applicable', 'pending', 'approved', 'rejected') DEFAULT 'not_applicable' COMMENT 'PWD approval status',
ADD COLUMN pwd_approved_by INT DEFAULT NULL COMMENT 'Admin ID who approved PWD status',
ADD COLUMN pwd_approved_at TIMESTAMP NULL DEFAULT NULL COMMENT 'When PWD status was approved',
ADD COLUMN pwd_rejection_reason TEXT DEFAULT NULL COMMENT 'Reason for PWD rejection (if applicable)';

-- Add foreign key constraint for pwd_approved_by (optional)
-- ALTER TABLE register_client 
-- ADD CONSTRAINT fk_pwd_approved_by 
-- FOREIGN KEY (pwd_approved_by) REFERENCES admin(id) ON DELETE SET NULL;

-- Update existing records to set proper defaults
UPDATE register_client 
SET 
    is_pwd = 0,
    commission_rate = 10.00,
    pwd_approval_status = 'not_applicable'
WHERE 
    is_pwd IS NULL 
    OR commission_rate IS NULL 
    OR pwd_approval_status IS NULL;

-- Verify the changes
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM 
    INFORMATION_SCHEMA.COLUMNS 
WHERE 
    TABLE_SCHEMA = 'giggenius' 
    AND TABLE_NAME = 'register_client' 
    AND COLUMN_NAME IN ('is_pwd', 'pwd_condition', 'pwd_proof', 'commission_rate', 'pwd_approval_status', 'pwd_approved_by', 'pwd_approved_at', 'pwd_rejection_reason')
ORDER BY 
    ORDINAL_POSITION;

-- Add the same PWD columns to approve_genius table
ALTER TABLE approve_genius
ADD COLUMN is_pwd TINYINT(1) DEFAULT 0 COMMENT 'Whether user is a Person with Disability (0=No, 1=Yes)',
ADD COLUMN pwd_condition TEXT DEFAULT NULL COMMENT 'Description of PWD condition (internal use only)',
ADD COLUMN pwd_proof LONGBLOB DEFAULT NULL COMMENT 'PWD proof document (binary data)',
ADD COLUMN commission_rate DECIMAL(5,2) DEFAULT 10.00 COMMENT 'Commission percentage (default 10.00%)',
ADD COLUMN pwd_approval_status ENUM('not_applicable', 'pending', 'approved', 'rejected') DEFAULT 'not_applicable' COMMENT 'PWD approval status',
ADD COLUMN pwd_approved_by INT DEFAULT NULL COMMENT 'Admin ID who approved PWD status',
ADD COLUMN pwd_approved_at TIMESTAMP NULL DEFAULT NULL COMMENT 'When PWD status was approved',
ADD COLUMN pwd_rejection_reason TEXT DEFAULT NULL COMMENT 'Reason for PWD rejection (if applicable)';

-- Update existing records in approve_genius table
UPDATE approve_genius
SET
    is_pwd = 0,
    commission_rate = 10.00,
    pwd_approval_status = 'not_applicable'
WHERE
    is_pwd IS NULL
    OR commission_rate IS NULL
    OR pwd_approval_status IS NULL;

-- Add the same PWD columns to approve_client table
ALTER TABLE approve_client
ADD COLUMN is_pwd TINYINT(1) DEFAULT 0 COMMENT 'Whether user is a Person with Disability (0=No, 1=Yes)',
ADD COLUMN pwd_condition TEXT DEFAULT NULL COMMENT 'Description of PWD condition (internal use only)',
ADD COLUMN pwd_proof LONGBLOB DEFAULT NULL COMMENT 'PWD proof document (binary data)',
ADD COLUMN commission_rate DECIMAL(5,2) DEFAULT 10.00 COMMENT 'Commission percentage (default 10.00%)',
ADD COLUMN pwd_approval_status ENUM('not_applicable', 'pending', 'approved', 'rejected') DEFAULT 'not_applicable' COMMENT 'PWD approval status',
ADD COLUMN pwd_approved_by INT DEFAULT NULL COMMENT 'Admin ID who approved PWD status',
ADD COLUMN pwd_approved_at TIMESTAMP NULL DEFAULT NULL COMMENT 'When PWD status was approved',
ADD COLUMN pwd_rejection_reason TEXT DEFAULT NULL COMMENT 'Reason for PWD rejection (if applicable)';

-- Update existing records in approve_client table
UPDATE approve_client
SET
    is_pwd = 0,
    commission_rate = 10.00,
    pwd_approval_status = 'not_applicable'
WHERE
    is_pwd IS NULL
    OR commission_rate IS NULL
    OR pwd_approval_status IS NULL;

-- Check if columns were added successfully to both tables
DESCRIBE register_client;
DESCRIBE approve_client;

-- Show sample data to verify
SELECT
    id, first_name, last_name, is_pwd, commission_rate, pwd_approval_status
FROM register_client
LIMIT 5;

SELECT
    id, first_name, last_name, is_pwd, commission_rate, pwd_approval_status
FROM approve_client
LIMIT 5;
