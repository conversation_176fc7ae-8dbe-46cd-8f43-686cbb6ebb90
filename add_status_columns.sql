-- SQL script to add status column to approve_client and approve_genius tables
-- Run these commands in your phpMyAdmin SQL tab or MySQL client

-- Add status column to approve_client table
ALTER TABLE approve_client
ADD COLUMN status ENUM('pending', 'approved', 'declined') DEFAULT 'pending' COMMENT 'Approval status of the client'
AFTER id;

-- Add status column to approve_genius table  
ALTER TABLE approve_genius
ADD COLUMN status ENUM('pending', 'approved', 'declined') DEFAULT 'pending' COMMENT 'Approval status of the genius'
AFTER id;

-- Update existing records to set default status as 'approved' since they are in the approve tables
UPDATE approve_client SET status = 'approved' WHERE status IS NULL OR status = 'pending';
UPDATE approve_genius SET status = 'approved' WHERE status IS NULL OR status = 'pending';

-- Verify the changes
DESCRI<PERSON> approve_client;
DESCRIBE approve_genius;

-- Show sample data to verify
SELECT id, status, first_name, last_name, work_email FROM approve_client LIMIT 5;
SELECT id, status, first_name, last_name, work_email FROM approve_genius LIMIT 5;
