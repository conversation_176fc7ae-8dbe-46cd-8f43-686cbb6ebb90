-- SQL script to add updated_at columns to approve_client and approve_genius tables
-- Run these commands in your phpMyAdmin SQL tab or MySQL client

-- Add updated_at column to approve_client table
ALTER TABLE approve_client
ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Last update timestamp'
AFTER created_at;

-- Add updated_at column to approve_genius table
ALTER TABLE approve_genius
ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Last update timestamp'
AFTER created_at;

-- Verify the changes
DESCRIBE approve_client;
DESCRIBE approve_genius;

-- Show sample data to verify
SELECT id, status, first_name, last_name, work_email, created_at, updated_at FROM approve_client LIMIT 5;
SELECT id, status, first_name, last_name, email, created_at, updated_at FROM approve_genius LIMIT 5;
