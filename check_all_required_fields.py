#!/usr/bin/env python3
"""
Check ALL required fields in approve_client vs register_client
"""

import mysql.connector

# Database configuration
DB_CONFIG = {
    'host': '**************',
    'user': 'giggenius_user',
    'password': 'Happiness1524!',
    'database': 'giggenius',
    'port': 3306,
    'connect_timeout': 10,
    'use_pure': True,
    'autocommit': False,
    'buffered': True,
    'consume_results': True
}

def check_all_required_fields():
    """Check ALL required fields and provide complete mapping"""
    try:
        print("🔗 Connecting to database...")
        conn = mysql.connector.connect(**DB_CONFIG)
        cursor = conn.cursor(dictionary=True)
        print("✅ Connected successfully!")
        
        # Get approve_client structure and find ALL required fields
        print("\n🔍 Checking approve_client table structure...")
        cursor.execute("DESCRIBE approve_client")
        app_columns = cursor.fetchall()
        
        required_fields = []
        all_fields = []
        
        print("\n📋 approve_client table analysis:")
        for col in app_columns:
            all_fields.append(col['Field'])
            is_required = col['Null'] == 'NO' and col['Default'] is None and col['Extra'] != 'auto_increment'
            status = "REQUIRED" if is_required else "optional"
            print(f"  - {col['Field']} ({col['Type']}) - {status}")
            if is_required:
                required_fields.append(col['Field'])
        
        print(f"\n🎯 ALL Required fields in approve_client: {required_fields}")
        
        # Get register_client structure
        print("\n🔍 Checking register_client table structure...")
        cursor.execute("DESCRIBE register_client")
        reg_columns = cursor.fetchall()
        
        reg_fields = []
        print("\n📋 register_client available fields:")
        for col in reg_columns:
            reg_fields.append(col['Field'])
            print(f"  - {col['Field']} ({col['Type']})")
        
        # Find missing required fields
        missing_fields = []
        available_fields = []
        
        print(f"\n🔍 Field mapping analysis:")
        for field in required_fields:
            if field in reg_fields:
                available_fields.append(field)
                print(f"  ✅ {field} - Available in register_client")
            else:
                missing_fields.append(field)
                print(f"  ❌ {field} - MISSING in register_client (needs default value)")
        
        print(f"\n📊 Summary:")
        print(f"  - Total required fields in approve_client: {len(required_fields)}")
        print(f"  - Available in register_client: {len(available_fields)}")
        print(f"  - Missing (need defaults): {len(missing_fields)}")
        
        if missing_fields:
            print(f"\n🚨 Missing required fields that need default values:")
            for field in missing_fields:
                print(f"  - {field}")
        
        # Generate SQL template
        print(f"\n💡 Suggested INSERT statement template:")
        print("INSERT INTO approve_client (")
        field_list = available_fields + missing_fields + ['status', 'updated_at']
        for i, field in enumerate(field_list):
            comma = "," if i < len(field_list) - 1 else ""
            print(f"    {field}{comma}")
        print(") VALUES (")
        for i, field in enumerate(field_list):
            comma = "," if i < len(field_list) - 1 else ""
            if field in available_fields:
                print(f"    %({field})s{comma}")
            elif field == 'status':
                print(f"    'approved'{comma}")
            elif field == 'updated_at':
                print(f"    NOW(){comma}")
            else:
                # Suggest default values based on field name
                if 'date' in field.lower() or 'birthday' in field.lower():
                    default = "'1990-01-01'"
                elif 'mobile' in field.lower() or 'phone' in field.lower():
                    default = "'************'"
                elif 'email' in field.lower():
                    default = "'<EMAIL>'"
                elif 'logo' in field.lower() or 'image' in field.lower():
                    default = "NULL"
                elif 'address' in field.lower():
                    default = "'Not provided'"
                elif 'website' in field.lower() or 'url' in field.lower():
                    default = "''"
                else:
                    default = "''"
                print(f"    {default}{comma}  -- Default for {field}")
        print(")")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        try:
            cursor.close()
            conn.close()
        except:
            pass

if __name__ == "__main__":
    check_all_required_fields()
