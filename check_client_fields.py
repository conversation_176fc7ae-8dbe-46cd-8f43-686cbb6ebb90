#!/usr/bin/env python3
"""
Check what fields are actually available in register_client vs approve_client
"""

import mysql.connector

# Database configuration
DB_CONFIG = {
    'host': '**************',
    'user': 'giggenius_user',
    'password': 'Happiness1524!',
    'database': 'giggenius',
    'port': 3306,
    'connect_timeout': 10,
    'use_pure': True,
    'autocommit': False,
    'buffered': True,
    'consume_results': True
}

def check_client_fields():
    """Check what fields are available in both tables"""
    try:
        print("🔗 Connecting to database...")
        conn = mysql.connector.connect(**DB_CONFIG)
        cursor = conn.cursor(dictionary=True)
        print("✅ Connected successfully!")
        
        # Check register_client structure
        print("\n🔍 Checking register_client table structure...")
        cursor.execute("DESCRIBE register_client")
        reg_columns = cursor.fetchall()
        
        print("\n📋 register_client columns:")
        reg_fields = set()
        for col in reg_columns:
            reg_fields.add(col['Field'])
            print(f"  - {col['Field']} ({col['Type']})")
        
        # Check approve_client structure and required fields
        print("\n🔍 Checking approve_client table structure...")
        cursor.execute("DESCRIBE approve_client")
        app_columns = cursor.fetchall()
        
        print("\n📋 approve_client columns:")
        app_fields = set()
        required_fields = []
        for col in app_columns:
            app_fields.add(col['Field'])
            is_required = col['Null'] == 'NO' and col['Default'] is None and col['Extra'] != 'auto_increment'
            status = "REQUIRED" if is_required else "optional"
            print(f"  - {col['Field']} ({col['Type']}) - {status}")
            if is_required:
                required_fields.append(col['Field'])
        
        print(f"\n🎯 Required fields in approve_client: {required_fields}")
        
        # Find missing fields
        missing_in_register = set(required_fields) - reg_fields
        if missing_in_register:
            print(f"\n❌ Required fields missing in register_client: {missing_in_register}")
        
        # Check sample data from register_client
        print("\n🔍 Sample register_client data:")
        cursor.execute("SELECT * FROM register_client LIMIT 1")
        sample = cursor.fetchone()
        if sample:
            print("Available fields in register_client:")
            for key, value in sample.items():
                print(f"  - {key}: {value}")
        else:
            print("No data in register_client table")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        try:
            cursor.close()
            conn.close()
        except:
            pass

if __name__ == "__main__":
    check_client_fields()
