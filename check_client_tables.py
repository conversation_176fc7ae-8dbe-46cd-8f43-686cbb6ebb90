#!/usr/bin/env python3
"""
Check client table structures
"""

import mysql.connector

# Database configuration
DB_CONFIG = {
    'host': '**************',
    'user': 'giggenius_user',
    'password': 'Happiness1524!',
    'database': 'giggenius',
    'port': 3306,
    'connect_timeout': 10,
    'use_pure': True,
    'autocommit': False,
    'buffered': True,
    'consume_results': True
}

def check_client_tables():
    """Check client table structures"""
    try:
        print("🔗 Connecting to database...")
        conn = mysql.connector.connect(**DB_CONFIG)
        cursor = conn.cursor(dictionary=True)
        print("✅ Connected successfully!")
        
        # Check register_client structure
        print("\n🔍 Checking register_client table structure...")
        cursor.execute("DESCRIBE register_client")
        reg_columns = cursor.fetchall()
        
        print("\n📋 register_client columns:")
        for col in reg_columns:
            print(f"  - {col['Field']} ({col['Type']})")
        
        # Check approve_client structure
        print("\n🔍 Checking approve_client table structure...")
        cursor.execute("DESCRIBE approve_client")
        app_columns = cursor.fetchall()
        
        print("\n📋 approve_client columns:")
        for col in app_columns:
            print(f"  - {col['Field']} ({col['Type']})")
        
        # Find differences
        reg_fields = {col['Field'] for col in reg_columns}
        app_fields = {col['Field'] for col in app_columns}
        
        missing_in_approve = reg_fields - app_fields
        missing_in_register = app_fields - reg_fields
        
        if missing_in_approve:
            print(f"\n❌ Columns missing in approve_client: {missing_in_approve}")
        
        if missing_in_register:
            print(f"\n❌ Columns missing in register_client: {missing_in_register}")
        
        # Check sample data
        print("\n🔍 Sample register_client data:")
        cursor.execute("SELECT * FROM register_client LIMIT 1")
        sample = cursor.fetchone()
        if sample:
            for key, value in sample.items():
                print(f"  - {key}: {value}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        try:
            cursor.close()
            conn.close()
        except:
            pass

if __name__ == "__main__":
    check_client_tables()
