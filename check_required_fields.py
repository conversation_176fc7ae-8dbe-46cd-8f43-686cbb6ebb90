#!/usr/bin/env python3
"""
Check which fields are required in approve_genius table
"""

import mysql.connector

# Database configuration
DB_CONFIG = {
    'host': '**************',
    'user': 'giggenius_user',
    'password': 'Happiness1524!',
    'database': 'giggenius',
    'port': 3306,
    'connect_timeout': 10,
    'use_pure': True,
    'autocommit': False,
    'buffered': True,
    'consume_results': True
}

def check_required_fields():
    """Check which fields are required (NOT NULL) in approve_client table"""
    try:
        print("🔗 Connecting to database...")
        conn = mysql.connector.connect(**DB_CONFIG)
        cursor = conn.cursor(dictionary=True)
        print("✅ Connected successfully!")

        # Get table structure for approve_client
        print("🔍 Checking approve_client table structure...")
        cursor.execute("DESCRIBE approve_client")
        columns = cursor.fetchall()

        print("\n📋 approve_client table structure:")
        required_fields = []
        for col in columns:
            is_required = col['Null'] == 'NO' and col['Default'] is None and col['Extra'] != 'auto_increment'
            status = "REQUIRED" if is_required else "optional"
            print(f"  - {col['Field']} ({col['Type']}) - {status}")
            if is_required:
                required_fields.append(col['Field'])

        print(f"\n🎯 Required fields (NOT NULL with no default): {required_fields}")

        # Now check what data we have in register_client for these fields
        print("\n🔍 Checking sample data from register_client...")
        cursor.execute("SELECT * FROM register_client LIMIT 1")
        sample_data = cursor.fetchone()

        if sample_data:
            print("\n📋 Sample register_client data:")
            for field in required_fields:
                if field in sample_data:
                    value = sample_data[field]
                    print(f"  - {field}: {value} ({type(value).__name__})")
                else:
                    print(f"  - {field}: NOT FOUND in register_client")

        # Also check register_client structure
        print("\n🔍 Checking register_client table structure...")
        cursor.execute("DESCRIBE register_client")
        reg_columns = cursor.fetchall()

        print("\n📋 register_client table structure:")
        for col in reg_columns:
            print(f"  - {col['Field']} ({col['Type']})")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        try:
            cursor.close()
            conn.close()
        except:
            pass

if __name__ == "__main__":
    check_required_fields()
