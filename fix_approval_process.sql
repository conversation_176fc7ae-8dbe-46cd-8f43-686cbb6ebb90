-- SQL script to fix the genius approval process
-- This script creates a proper approval process that works with the current table structures

-- First, let's check what data we have in register_genius
SELECT 'Current register_genius data:' as info;
SELECT id, first_name, last_name, email, country, status FROM register_genius;

-- Check what data we have in approve_genius
SELECT 'Current approve_genius data:' as info;
SELECT id, first_name, last_name, email, country, status FROM approve_genius;

-- Create a stored procedure to properly move genius from register to approve
DELIMITER //

CREATE PROCEDURE ApproveGenius(IN genius_id INT)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- Insert into approve_genius with only the common columns
    INSERT INTO approve_genius (
        profile_photo, first_name, last_name, email, password,
        birthday, country, mobile, position, expertise, hourly_rate,
        availability, tax_id, introduction, professional_sum,
        id_front, id_back, email_updates, terms_agreement,
        is_pwd, pwd_condition, pwd_proof, commission_rate,
        pwd_approval_status, pwd_approved_by, pwd_approved_at,
        pwd_rejection_reason, status, created_at, updated_at
    )
    SELECT 
        profile_photo, first_name, last_name, email, password,
        birthday, country, mobile, position, expertise, hourly_rate,
        availability, tax_id, introduction, professional_sum,
        id_front, id_back, email_updates, terms_agreement,
        is_pwd, pwd_condition, pwd_proof, commission_rate,
        pwd_approval_status, pwd_approved_by, pwd_approved_at,
        pwd_rejection_reason, 'approved', created_at, NOW()
    FROM register_genius 
    WHERE id = genius_id;
    
    -- Delete from register_genius
    DELETE FROM register_genius WHERE id = genius_id;
    
    COMMIT;
END //

DELIMITER ;

-- Test the procedure with one of the existing geniuses
-- First, let's see what geniuses are available to approve
SELECT 'Available geniuses to approve:' as info;
SELECT id, first_name, last_name, email, country FROM register_genius WHERE status = 'pending' LIMIT 3;

-- You can manually call this procedure to approve a genius:
-- CALL ApproveGenius(16);  -- Replace 16 with the actual ID you want to approve

-- Check the results after calling the procedure
-- SELECT 'After approval - register_genius:' as info;
-- SELECT id, first_name, last_name, email, country FROM register_genius;

-- SELECT 'After approval - approve_genius:' as info;
-- SELECT id, first_name, last_name, email, country FROM approve_genius ORDER BY id DESC LIMIT 5;
