from flask import Flask, request, redirect, url_for, render_template_string, send_from_directory
import mysql.connector
import os
from werkzeug.utils import secure_filename

app = Flask(__name__)

# 📂 Upload folder and size config
UPLOAD_FOLDER = '/var/www/GigGenius/uploads'
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
app.config['MAX_CONTENT_LENGTH'] = 256 * 1024 * 1024  # 256MB

# 🧠 DB config
db_config = {
    'host': '**************',
    'user': 'giggenius_user',
    'password': 'Happiness1524!',
    'database': 'giggenius',
}

# 🏠 Homepage: list videos
@app.route('/')
def index():
    try:
        db = mysql.connector.connect(**db_config)
        cursor = db.cursor()
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS video_files (
                id INT AUTO_INCREMENT PRIMARY KEY,
                filename VARCHAR(255) NOT NULL,
                filepath VARCHAR(255) NOT NULL,
                file_size INT NOT NULL,
                uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        cursor.execute("SELECT filename FROM video_files ORDER BY uploaded_at DESC")
        videos = cursor.fetchall()
        cursor.close()
        db.close()

        # 🖥️ Display simple HTML
        return render_template_string('''
            <h1>🎬 Uploaded Videos</h1>
            <ul>
            {% for video in videos %}
                <li><a href="{{ url_for('serve_video', filename=video[0]) }}" target="_blank">{{ video[0] }}</a></li>
            {% endfor %}
            </ul>
            <p><a href="{{ url_for('upload_video') }}">Upload another video</a></p>
        ''', videos=videos)

    except Exception as e:
        return f"<p>Error loading videos: {e}</p>", 500

# 📤 Upload video
@app.route('/upload', methods=['GET', 'POST'])
def upload_video():
    if request.method == 'POST':
        file = request.files.get('video')
        if not file:
            return "No file selected", 400

        filename = secure_filename(file.filename)
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        file.save(file_path)

        file.seek(0, 2)
        file_size = file.tell()

        try:
            db = mysql.connector.connect(**db_config)
            cursor = db.cursor()
            cursor.execute("INSERT INTO video_files (filename, filepath, file_size) VALUES (%s, %s, %s)",
                           (filename, file_path, file_size))
            db.commit()
            cursor.close()
            db.close()
        except Exception as e:
            return f"Database error: {e}", 500

        return redirect(url_for('index'))

    # GET form
    return '''
    <h1>Upload a Video</h1>
    <form method="post" enctype="multipart/form-data">
      <input type="file" name="video" accept="video/*" required><br><br>
      <input type="submit" value="Upload">
    </form>
    <p><a href="/">← Back to video list</a></p>
    '''

# 🎥 Serve video
@app.route('/uploads/<filename>')
def serve_video(filename):
    return send_from_directory(app.config['UPLOAD_FOLDER'], filename)

# ✅ Test run
if __name__ == '__main__':
    app.run(debug=True)
