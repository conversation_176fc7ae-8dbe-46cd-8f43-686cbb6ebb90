from flask import Flask, request, jsonify
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIME<PERSON>ult<PERSON>art
from datetime import datetime

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-for-sample'



# 📧 EMAIL CONFIGURATION
EMAIL_CONFIG = {
    'smtp_server': 'smtp.hostinger.com',  # Try 'mail.gig-genius.io' if this doesn't work
    'smtp_port': 587,  # Try 465 if 587 doesn't work
    'email': '<EMAIL>',  # This sends all emails
    'password': 'Happiness1524$',  # Your Hostinger password
    'use_tls': True  # Try False if True doesn't work
}

# 🎯 EMAIL ADDRESSES
ADMIN_EMAIL = '<EMAIL>'  # Admin gets notifications here
TEST_EMAIL = '<EMAIL>'  # For testing only

# 📊 Simple in-memory storage for demo (replace with database in production)
users_db = []
admin_notifications = []

def send_admin_notification(subject, message, priority='normal'):
    """
    Send admin <NAME_EMAIL>

    Args:
        subject (str): Email subject
        message (str): Email message content
        priority (str): Email priority (low, normal, high)

    Returns:
        dict: Success status and message
    """
    return send_email_notification(
        recipient_email=ADMIN_EMAIL,
        subject=subject,
        message=message,
        priority=priority
    )

def send_user_notification(recipient_email, subject, message, priority='normal'):
    """
    Send user <NAME_EMAIL>

    Args:
        recipient_email (str): Email address to send to
        subject (str): Email subject
        message (str): Email message content
        priority (str): Email priority (low, normal, high)

    Returns:
        dict: Success status and message
    """
    return send_email_notification(
        recipient_email=recipient_email,
        subject=subject,
        message=message,
        priority=priority
    )

def send_email_notification(recipient_email, subject, message, priority='normal'):
    """
    Send email <NAME_EMAIL>

    Args:
        recipient_email (str): Email address to send to
        subject (str): Email subject
        message (str): Email message content
        priority (str): Email priority (low, normal, high)

    Returns:
        dict: Success status and message
    """
    try:
        # Validate email configuration
        if not EMAIL_CONFIG['email']:
            return {
                'success': False,
                'error': 'Email not configured. Please update EMAIL_CONFIG.'
            }

        if not EMAIL_CONFIG['password'] or EMAIL_CONFIG['password'] == 'your-hostinger-password':
            return {
                'success': False,
                'error': 'Email password not configured. Please update EMAIL_CONFIG with your actual Hostinger password.'
            }

        print(f"� Sending email FROM: {EMAIL_CONFIG['email']} TO: {recipient_email}")
        print(f"🌐 SMTP Server: {EMAIL_CONFIG['smtp_server']}:{EMAIL_CONFIG['smtp_port']}")
        print(f"📝 Subject: {subject}")
        print(f"🔑 Using password: {'*' * (len(EMAIL_CONFIG['password']) - 4)}{EMAIL_CONFIG['password'][-4:]}")

        # Create message
        msg = MIMEMultipart()
        msg['From'] = EMAIL_CONFIG['email']
        msg['To'] = recipient_email
        msg['Subject'] = subject

        # Set priority
        if priority == 'high':
            msg['X-Priority'] = '1'
            msg['X-MSMail-Priority'] = 'High'
        elif priority == 'low':
            msg['X-Priority'] = '5'
            msg['X-MSMail-Priority'] = 'Low'
        else:
            msg['X-Priority'] = '3'
            msg['X-MSMail-Priority'] = 'Normal'

        # Add timestamp and sender info
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        email_body = f"""
{message}

---
Sent from GigGenius Platform
Time: {timestamp}
Priority: {priority.capitalize()}
        """

        # Attach message body
        msg.attach(MIMEText(email_body, 'plain'))

        # Create SMTP session with timeout
        print("🔌 Connecting to SMTP server...")
        server = smtplib.SMTP(EMAIL_CONFIG['smtp_server'], EMAIL_CONFIG['smtp_port'], timeout=30)
        server.set_debuglevel(1)  # Enable debug output

        print("🔐 Starting TLS...")
        if EMAIL_CONFIG['use_tls']:
            server.starttls()  # Enable TLS encryption

        print("🔑 Attempting login...")
        # Login and send email
        server.login(EMAIL_CONFIG['email'], EMAIL_CONFIG['password'])

        print("📤 Sending email...")
        text = msg.as_string()
        server.sendmail(EMAIL_CONFIG['email'], recipient_email, text)
        server.quit()

        print("✅ Email sent successfully!")
        return {
            'success': True,
            'message': f'Email sent successfully to {recipient_email}'
        }

    except smtplib.SMTPAuthenticationError as e:
        error_msg = f"Authentication failed: {str(e)}"
        print(f"❌ {error_msg}")
        return {
            'success': False,
            'error': f'Gmail authentication failed. Please check: 1) Email address is correct, 2) You are using App Password (not regular password), 3) 2-Factor Authentication is enabled. Error: {str(e)}'
        }
    except smtplib.SMTPRecipientsRefused as e:
        error_msg = f"Recipients refused: {str(e)}"
        print(f"❌ {error_msg}")
        return {
            'success': False,
            'error': f'Invalid recipient email address: {str(e)}'
        }
    except smtplib.SMTPServerDisconnected as e:
        error_msg = f"Server disconnected: {str(e)}"
        print(f"❌ {error_msg}")
        return {
            'success': False,
            'error': f'SMTP server connection failed: {str(e)}'
        }
    except smtplib.SMTPConnectError as e:
        error_msg = f"Connection error: {str(e)}"
        print(f"❌ {error_msg}")
        return {
            'success': False,
            'error': f'Could not connect to SMTP server: {str(e)}'
        }
    except Exception as e:
        error_msg = f"Unexpected error: {str(e)}"
        print(f"❌ {error_msg}")
        return {
            'success': False,
            'error': f'Failed to send email: {str(e)}'
        }

@app.route('/')
def index():
    """Home page with navigation"""
    return main_page_html()

@app.route('/email_form')
def email_form():
    """Display the email form page"""
    return email_form_html()

@app.route('/register_demo')
def register_demo():
    """Display user registration demo page"""
    return registration_demo_html()

@app.route('/admin_panel')
def admin_panel():
    """Display admin panel to view notifications"""
    return admin_panel_html()

def main_page_html():
    """Return the main navigation page HTML"""
    return '''
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GigGenius Email System Demo</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .main-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            width: 100%;
            max-width: 800px;
            text-align: center;
        }

        .logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea, #e91e63);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 30px;
            color: white;
            font-size: 32px;
            font-weight: bold;
        }

        h1 {
            color: #333;
            font-size: 32px;
            font-weight: 600;
            margin-bottom: 15px;
        }

        .subtitle {
            color: #666;
            font-size: 18px;
            margin-bottom: 40px;
        }

        .nav-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .nav-card {
            background: #f8f9fa;
            border: 2px solid #e1e5e9;
            border-radius: 15px;
            padding: 30px 20px;
            text-decoration: none;
            color: #333;
            transition: all 0.3s ease;
        }

        .nav-card:hover {
            transform: translateY(-5px);
            border-color: #667eea;
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.2);
        }

        .nav-card i {
            font-size: 48px;
            color: #667eea;
            margin-bottom: 15px;
        }

        .nav-card h3 {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .nav-card p {
            font-size: 14px;
            color: #666;
        }

        .config-info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 30px;
            text-align: left;
        }

        .config-info h4 {
            color: #333;
            margin-bottom: 15px;
        }

        .config-item {
            margin-bottom: 10px;
            font-size: 14px;
        }

        .config-item strong {
            color: #667eea;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="logo">G</div>
        <h1>GigGenius Email System</h1>
        <p class="subtitle">Simple Email System - <EMAIL> sends all emails</p>

        <div class="nav-grid">
            <a href="/register_demo" class="nav-card">
                <i class="bi bi-person-plus"></i>
                <h3>User Registration</h3>
                <p>Test user registration with dual email notifications</p>
            </a>

            <a href="/email_form" class="nav-card">
                <i class="bi bi-envelope"></i>
                <h3>Email Form</h3>
                <p>Send custom emails using Gmail configuration</p>
            </a>

            <a href="/admin_panel" class="nav-card">
                <i class="bi bi-shield-check"></i>
                <h3>Admin Panel</h3>
                <p>View admin notifications and user registrations</p>
            </a>

            <a href="/test_email" class="nav-card">
                <i class="bi bi-envelope-check"></i>
                <h3>Test Email</h3>
                <p>Send test email to your address</p>
            </a>

            <a href="/test_admin_email" class="nav-card">
                <i class="bi bi-shield-exclamation"></i>
                <h3>Test Admin Email</h3>
                <p>Send test admin notification</p>
            </a>

            <a href="/health" class="nav-card">
                <i class="bi bi-heart-pulse"></i>
                <h3>Health Check</h3>
                <p>Check system status and configuration</p>
            </a>
        </div>

        <div class="config-info">
            <h4>📧 Simple Email Flow</h4>
            <div class="config-item"><strong>📤 Sender:</strong> <EMAIL> (Hostinger SMTP)</div>
            <div class="config-item"><strong>📧 Admin notifications:</strong> → <EMAIL></div>
            <div class="config-item"><strong>📧 User notifications:</strong> → User's email address</div>
            <div class="config-item"><strong>📧 Test emails:</strong> → <EMAIL></div>
        </div>
    </div>
</body>
</html>
    '''

def email_form_html():
    """Return the email form HTML"""
    return '''
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Send Email - GigGenius Sample</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .email-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            width: 100%;
            max-width: 600px;
            position: relative;
            overflow: hidden;
        }

        .email-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, #667eea, #e91e63);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .logo {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea, #e91e63);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            color: white;
            font-size: 24px;
            font-weight: bold;
        }

        h1 {
            color: #333;
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .subtitle {
            color: #666;
            font-size: 16px;
        }

        .form-group {
            margin-bottom: 25px;
            position: relative;
        }

        label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
            font-size: 14px;
        }

        .required {
            color: #e91e63;
        }

        input[type="email"],
        input[type="text"],
        textarea,
        select {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid #e1e5e9;
            border-radius: 12px;
            font-size: 16px;
            font-family: 'Poppins', sans-serif;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        input[type="email"]:focus,
        input[type="text"]:focus,
        textarea:focus,
        select:focus {
            outline: none;
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        textarea {
            resize: vertical;
            min-height: 120px;
        }

        .btn-send {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-send:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }

        .btn-send:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none;
        }

        .loading {
            display: none;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .spinner {
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .toast {
            position: fixed;
            bottom: 30px;
            right: 30px;
            padding: 15px 25px;
            border-radius: 12px;
            color: white;
            font-weight: 500;
            z-index: 1000;
            transform: translateX(400px);
            transition: transform 0.3s ease;
            max-width: 350px;
        }

        .toast.show {
            transform: translateX(0);
        }

        .toast.success {
            background: linear-gradient(135deg, #28a745, #20c997);
        }

        .toast.error {
            background: linear-gradient(135deg, #dc3545, #e91e63);
        }

        .nav-links {
            text-align: center;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #e1e5e9;
        }

        .nav-links a {
            color: #667eea;
            text-decoration: none;
            margin: 0 15px;
            font-weight: 500;
        }

        .nav-links a:hover {
            text-decoration: underline;
        }

        @media (max-width: 768px) {
            .email-container {
                padding: 30px 20px;
                margin: 10px;
            }

            h1 {
                font-size: 24px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header">
            <div class="logo">G</div>
            <h1>Send Email</h1>
            <p class="subtitle">GigGenius Email Sample App</p>
        </div>

        <form id="emailForm">
            <div class="form-group">
                <label for="recipient">Recipient Email <span class="required">*</span></label>
                <input type="email" id="recipient" name="recipient" required placeholder="Enter recipient email address">
            </div>

            <div class="form-group">
                <label for="subject">Subject <span class="required">*</span></label>
                <input type="text" id="subject" name="subject" required placeholder="Enter email subject">
            </div>

            <div class="form-group">
                <label for="message">Message <span class="required">*</span></label>
                <textarea id="message" name="message" required placeholder="Enter your message here..."></textarea>
            </div>

            <div class="form-group">
                <label for="priority">Priority</label>
                <select id="priority" name="priority">
                    <option value="normal">Normal</option>
                    <option value="high">High</option>
                    <option value="low">Low</option>
                </select>
            </div>

            <button type="submit" class="btn-send" id="sendBtn">
                <span class="btn-text">Send Email</span>
                <div class="loading">
                    <div class="spinner"></div>
                    <span>Sending...</span>
                </div>
            </button>
        </form>

        <div class="nav-links">
            <a href="/">Home</a>
            <a href="/test_email">Quick Test Email</a>
            <a href="/health">Health Check</a>
        </div>
    </div>

    <script>
        const form = document.getElementById('emailForm');
        const sendBtn = document.getElementById('sendBtn');
        const btnText = document.querySelector('.btn-text');
        const loading = document.querySelector('.loading');

        // Show toast notification
        function showToast(message, type = 'success') {
            const toast = document.createElement('div');
            toast.className = `toast ${type}`;
            toast.textContent = message;
            document.body.appendChild(toast);

            setTimeout(() => toast.classList.add('show'), 100);
            setTimeout(() => {
                toast.classList.remove('show');
                setTimeout(() => document.body.removeChild(toast), 300);
            }, 4000);
        }

        // Handle form submission
        form.addEventListener('submit', async function(e) {
            e.preventDefault();

            // Show loading state
            sendBtn.disabled = true;
            btnText.style.display = 'none';
            loading.style.display = 'flex';

            try {
                const formData = new FormData(form);

                const response = await fetch('/send_email', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                if (data.success) {
                    showToast('Email sent successfully!', 'success');
                    form.reset();
                } else {
                    showToast(data.error || 'Failed to send email', 'error');
                }
            } catch (error) {
                console.error('Error:', error);
                showToast('An error occurred while sending email', 'error');
            } finally {
                // Reset button state
                sendBtn.disabled = false;
                btnText.style.display = 'inline';
                loading.style.display = 'none';
            }
        });
    </script>
</body>
</html>
    '''

def registration_demo_html():
    """Return the user registration demo HTML"""
    return '''
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Registration Demo - GigGenius</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .registration-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            width: 100%;
            max-width: 600px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .logo {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea, #e91e63);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            color: white;
            font-size: 24px;
            font-weight: bold;
        }

        h1 {
            color: #333;
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .subtitle {
            color: #666;
            font-size: 16px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
            font-size: 14px;
        }

        .required {
            color: #e91e63;
        }

        input[type="text"],
        input[type="email"],
        select {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid #e1e5e9;
            border-radius: 12px;
            font-size: 16px;
            font-family: 'Poppins', sans-serif;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        input[type="text"]:focus,
        input[type="email"]:focus,
        select:focus {
            outline: none;
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .btn-register {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-register:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }

        .btn-register:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none;
        }

        .loading {
            display: none;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .spinner {
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .toast {
            position: fixed;
            bottom: 30px;
            right: 30px;
            padding: 15px 25px;
            border-radius: 12px;
            color: white;
            font-weight: 500;
            z-index: 1000;
            transform: translateX(400px);
            transition: transform 0.3s ease;
            max-width: 350px;
        }

        .toast.show {
            transform: translateX(0);
        }

        .toast.success {
            background: linear-gradient(135deg, #28a745, #20c997);
        }

        .toast.error {
            background: linear-gradient(135deg, #dc3545, #e91e63);
        }

        .nav-links {
            text-align: center;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #e1e5e9;
        }

        .nav-links a {
            color: #667eea;
            text-decoration: none;
            margin: 0 15px;
            font-weight: 500;
        }

        .nav-links a:hover {
            text-decoration: underline;
        }

        .info-box {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            font-size: 14px;
            color: #1976d2;
        }
    </style>
</head>
<body>
    <div class="registration-container">
        <div class="header">
            <div class="logo">G</div>
            <h1>User Registration Demo</h1>
            <p class="subtitle">Test dual email notification system</p>
        </div>

        <div class="info-box">
            <strong>📧 Simple Email Flow:</strong><br>
            1. User registers with their email<br>
            2. <strong><EMAIL></strong> → user's email (confirmation)<br>
            3. <strong><EMAIL></strong> → <EMAIL> (admin notification)<br>
            <em>One sender, two recipients - simple and clean!</em>
        </div>

        <form id="registrationForm">
            <div class="form-group">
                <label for="user_type">User Type <span class="required">*</span></label>
                <select id="user_type" name="user_type" required>
                    <option value="">Select user type</option>
                    <option value="genius">Genius (Freelancer)</option>
                    <option value="client">Client (Employer)</option>
                </select>
            </div>

            <div class="form-group">
                <label for="first_name">First Name <span class="required">*</span></label>
                <input type="text" id="first_name" name="first_name" required placeholder="Enter first name">
            </div>

            <div class="form-group">
                <label for="last_name">Last Name <span class="required">*</span></label>
                <input type="text" id="last_name" name="last_name" required placeholder="Enter last name">
            </div>

            <div class="form-group">
                <label for="email">Email Address <span class="required">*</span></label>
                <input type="email" id="email" name="email" required placeholder="Enter email address">
            </div>

            <div class="form-group">
                <label for="country">Country <span class="required">*</span></label>
                <input type="text" id="country" name="country" required placeholder="Enter country">
            </div>

            <div class="form-group">
                <label for="position">Position/Title <span class="required">*</span></label>
                <input type="text" id="position" name="position" required placeholder="Enter position or job title">
            </div>

            <button type="submit" class="btn-register" id="registerBtn">
                <span class="btn-text">Register User</span>
                <div class="loading">
                    <div class="spinner"></div>
                    <span>Registering...</span>
                </div>
            </button>
        </form>

        <div class="nav-links">
            <a href="/">Home</a>
            <a href="/admin_panel">Admin Panel</a>
            <a href="/email_form">Email Form</a>
        </div>
    </div>

    <script>
        const form = document.getElementById('registrationForm');
        const registerBtn = document.getElementById('registerBtn');
        const btnText = document.querySelector('.btn-text');
        const loading = document.querySelector('.loading');

        // Show toast notification
        function showToast(message, type = 'success') {
            const toast = document.createElement('div');
            toast.className = `toast ${type}`;
            toast.textContent = message;
            document.body.appendChild(toast);

            setTimeout(() => toast.classList.add('show'), 100);
            setTimeout(() => {
                toast.classList.remove('show');
                setTimeout(() => document.body.removeChild(toast), 300);
            }, 4000);
        }

        // Handle form submission
        form.addEventListener('submit', async function(e) {
            e.preventDefault();

            // Show loading state
            registerBtn.disabled = true;
            btnText.style.display = 'none';
            loading.style.display = 'flex';

            try {
                const formData = new FormData(form);

                const response = await fetch('/register_user', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                if (data.success) {
                    showToast('Registration successful! Check emails for notifications.', 'success');
                    form.reset();
                } else {
                    showToast(data.error || 'Registration failed', 'error');
                }
            } catch (error) {
                console.error('Error:', error);
                showToast('An error occurred during registration', 'error');
            } finally {
                // Reset button state
                registerBtn.disabled = false;
                btnText.style.display = 'inline';
                loading.style.display = 'none';
            }
        });
    </script>
</body>
</html>
    '''

def admin_panel_html():
    """Return the admin panel HTML"""
    users_list = ""
    for user in users_db:
        users_list += f"""
        <tr>
            <td>{user['id']}</td>
            <td>{user['user_type'].title()}</td>
            <td>{user['first_name']} {user['last_name']}</td>
            <td>{user['email']}</td>
            <td>{user['country']}</td>
            <td>{user['position']}</td>
            <td><span class="status-{user['status']}">{user['status'].title()}</span></td>
            <td>{user['created_at']}</td>
        </tr>
        """

    notifications_list = ""
    for notif in admin_notifications:
        notifications_list += f"""
        <tr>
            <td>{notif['id']}</td>
            <td>{notif['type'].replace('_', ' ').title()}</td>
            <td>{notif['user_name']}</td>
            <td>{notif['user_email']}</td>
            <td>{'✅' if notif['admin_email_sent'] else '❌'}</td>
            <td>{'✅' if notif['user_email_sent'] else '❌'}</td>
            <td>{notif['created_at']}</td>
        </tr>
        """

    return f'''
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Panel - GigGenius</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}

        body {{
            font-family: 'Poppins', sans-serif;
            background: #f5f6fa;
            min-height: 100vh;
            padding: 20px;
        }}

        .admin-container {{
            max-width: 1200px;
            margin: 0 auto;
        }}

        .header {{
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            text-align: center;
        }}

        .logo {{
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea, #e91e63);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            color: white;
            font-size: 24px;
            font-weight: bold;
        }}

        h1 {{
            color: #333;
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 10px;
        }}

        .subtitle {{
            color: #666;
            font-size: 16px;
        }}

        .stats-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }}

        .stat-card {{
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            text-align: center;
        }}

        .stat-card i {{
            font-size: 40px;
            color: #667eea;
            margin-bottom: 15px;
        }}

        .stat-card h3 {{
            font-size: 32px;
            font-weight: 700;
            color: #333;
            margin-bottom: 5px;
        }}

        .stat-card p {{
            color: #666;
            font-size: 14px;
        }}

        .section {{
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }}

        .section h2 {{
            color: #333;
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }}

        .section h2 i {{
            color: #667eea;
        }}

        table {{
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }}

        th, td {{
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e1e5e9;
        }}

        th {{
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
        }}

        .status-pending {{
            background: #fff3cd;
            color: #856404;
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
        }}

        .status-approved {{
            background: #d4edda;
            color: #155724;
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
        }}

        .nav-links {{
            text-align: center;
            margin-top: 30px;
        }}

        .nav-links a {{
            color: #667eea;
            text-decoration: none;
            margin: 0 15px;
            font-weight: 500;
            padding: 10px 20px;
            border: 2px solid #667eea;
            border-radius: 8px;
            transition: all 0.3s ease;
        }}

        .nav-links a:hover {{
            background: #667eea;
            color: white;
        }}

        .empty-state {{
            text-align: center;
            padding: 40px;
            color: #666;
        }}

        .empty-state i {{
            font-size: 48px;
            color: #ccc;
            margin-bottom: 15px;
        }}

        .refresh-btn {{
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            margin-bottom: 20px;
        }}

        .refresh-btn:hover {{
            background: #5a6fd8;
        }}
    </style>
</head>
<body>
    <div class="admin-container">
        <div class="header">
            <div class="logo">A</div>
            <h1>Admin Panel</h1>
            <p class="subtitle">Monitor user registrations and email notifications</p>
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <i class="bi bi-people"></i>
                <h3>{len(users_db)}</h3>
                <p>Total Users</p>
            </div>
            <div class="stat-card">
                <i class="bi bi-bell"></i>
                <h3>{len(admin_notifications)}</h3>
                <p>Notifications</p>
            </div>
            <div class="stat-card">
                <i class="bi bi-envelope-check"></i>
                <h3>{sum(1 for n in admin_notifications if n['admin_email_sent'])}</h3>
                <p>Admin Emails Sent</p>
            </div>
            <div class="stat-card">
                <i class="bi bi-envelope-heart"></i>
                <h3>{sum(1 for n in admin_notifications if n['user_email_sent'])}</h3>
                <p>User Emails Sent</p>
            </div>
        </div>

        <div class="section">
            <h2><i class="bi bi-people"></i> User Registrations</h2>
            <button class="refresh-btn" onclick="location.reload()">
                <i class="bi bi-arrow-clockwise"></i> Refresh
            </button>
            {f'<div class="empty-state"><i class="bi bi-inbox"></i><h3>No users registered yet</h3><p>Users will appear here when they register through the demo form.</p></div>' if not users_db else f'<table><thead><tr><th>ID</th><th>Type</th><th>Name</th><th>Email</th><th>Country</th><th>Position</th><th>Status</th><th>Created</th></tr></thead><tbody>{users_list}</tbody></table>'}
        </div>

        <div class="section">
            <h2><i class="bi bi-bell"></i> Email Notifications</h2>
            {f'<div class="empty-state"><i class="bi bi-envelope"></i><h3>No notifications yet</h3><p>Email notifications will appear here when users register.</p></div>' if not admin_notifications else f'<table><thead><tr><th>ID</th><th>Type</th><th>User</th><th>Email</th><th>Admin Email</th><th>User Email</th><th>Created</th></tr></thead><tbody>{notifications_list}</tbody></table>'}
        </div>

        <div class="nav-links">
            <a href="/"><i class="bi bi-house"></i> Home</a>
            <a href="/register_demo"><i class="bi bi-person-plus"></i> Register Demo</a>
            <a href="/test_admin_email"><i class="bi bi-envelope-exclamation"></i> Test Admin Email</a>
        </div>
    </div>
</body>
</html>
    '''

@app.route('/send_email', methods=['POST'])
def send_email():
    """Handle email sending from the form - ALWAYS sends to YOUR email address"""
    try:
        # Get form data
        form_recipient = request.form.get('recipient')  # What user entered (for display only)
        subject = request.form.get('subject')
        message = request.form.get('message')
        priority = request.form.get('priority', 'normal')

        # Validate required fields
        if not all([form_recipient, subject, message]):
            return jsonify({
                'success': False,
                'error': 'All fields (recipient, subject, message) are required'
            })

        # Create notification message for YOU
        notification_subject = f"🔔 Form Submission: {subject}"
        notification_message = f"""
You received a new form submission from your website!

Original Recipient: {form_recipient}
Subject: {subject}
Priority: {priority.capitalize()}

Message:
{message}

---
This notification was triggered when someone clicked 'Send' on your email form.
        """

        # ALWAYS send to YOUR email address (not what user entered)
        result = send_user_notification(TEST_EMAIL, notification_subject, notification_message, priority)

        # Log email sending attempt
        print(f"📧 Form submission notification sent to: {TEST_EMAIL}")
        print(f"📝 Original form data - Recipient: {form_recipient}, Subject: {subject}")
        print(f"✅ Success: {result['success']}")

        if result['success']:
            return jsonify({
                'success': True,
                'message': f'Form submitted successfully! Notification sent to your email.'
            })
        else:
            return jsonify(result)

    except Exception as e:
        print(f"Error in send_email route: {e}")
        return jsonify({
            'success': False,
            'error': f'An error occurred: {str(e)}'
        })

@app.route('/register_user', methods=['POST'])
def register_user():
    """Handle user registration and send notifications"""
    try:
        # Get form data
        user_type = request.form.get('user_type')  # 'genius' or 'client'
        first_name = request.form.get('first_name')
        last_name = request.form.get('last_name')
        email = request.form.get('email')
        country = request.form.get('country')
        position = request.form.get('position')

        # Validate required fields
        if not all([user_type, first_name, last_name, email, country, position]):
            return jsonify({
                'success': False,
                'error': 'All fields are required'
            })

        # Create user record
        user_id = len(users_db) + 1
        user_data = {
            'id': user_id,
            'user_type': user_type,
            'first_name': first_name,
            'last_name': last_name,
            'email': email,
            'country': country,
            'position': position,
            'status': 'pending',
            'created_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        users_db.append(user_data)

        # Send admin notification via Hostinger
        admin_subject = f"🔔 New {user_type.title()} Registration - {first_name} {last_name}"
        admin_message = f"""
A new {user_type} has registered on the GigGenius platform!

User Details:
- Name: {first_name} {last_name}
- Email: {email}
- Country: {country}
- Position: {position}
- User Type: {user_type.title()}
- Registration Time: {user_data['created_at']}

Please review and approve this registration in the admin panel.

User ID: {user_id}
        """

        admin_result = send_admin_notification(admin_subject, admin_message, 'high')

        # Send user confirmation via Gmail
        user_subject = f"Welcome to GigGenius - Registration Received"
        user_message = f"""
Dear {first_name} {last_name},

Thank you for registering as a {user_type} on the GigGenius platform!

Your registration details:
- Name: {first_name} {last_name}
- Email: {email}
- Country: {country}
- Position: {position}

Your application is currently under review. We will notify you via email once your account has been approved.

Thank you for choosing GigGenius!

Best regards,
The GigGenius Team
        """

        user_result = send_user_notification(email, user_subject, user_message, 'normal')

        # Store notification in admin panel
        notification_data = {
            'id': len(admin_notifications) + 1,
            'type': 'user_registration',
            'user_id': user_id,
            'user_type': user_type,
            'user_name': f"{first_name} {last_name}",
            'user_email': email,
            'message': f"New {user_type} registration: {first_name} {last_name}",
            'admin_email_sent': admin_result['success'],
            'user_email_sent': user_result['success'],
            'created_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        admin_notifications.append(notification_data)

        return jsonify({
            'success': True,
            'message': f'Registration successful! Admin has been notified via Hostinger email, and confirmation sent to {email} via Gmail.',
            'user_id': user_id,
            'admin_notification': admin_result['success'],
            'user_notification': user_result['success']
        })

    except Exception as e:
        print(f"Error in register_user route: {e}")
        return jsonify({
            'success': False,
            'error': f'Registration failed: {str(e)}'
        })

@app.route('/test_email')
def test_email():
    """Quick test endpoint to send a test email to YOUR email"""
    try:
        # Send a test email to YOUR email address
        test_subject = "🧪 Test Email from GigGenius Sample App"
        test_message = "This is a test email to verify that the email functionality is working correctly! If you receive this, your email trigger is working perfectly."

        result = send_user_notification(TEST_EMAIL, test_subject, test_message, 'normal')

        if result['success']:
            return f"""
            <h2>✅ Test Email Sent Successfully!</h2>
            <p>A test email has been sent to: <strong>{TEST_EMAIL}</strong></p>
            <p>Check your inbox to confirm the email was received.</p>
            <br>
            <a href="/email_form">Go to Email Form</a>
            """
        else:
            return f"""
            <h2>❌ Test Email Failed</h2>
            <p>Error: {result['error']}</p>
            <p>Please check your email configuration.</p>
            <br>
            <a href="/email_form">Go to Email Form</a>
            """

    except Exception as e:
        return f"""
        <h2>❌ Test Email Error</h2>
        <p>Exception: {str(e)}</p>
        <p>Please check your email configuration and try again.</p>
        <br>
        <a href="/email_form">Go to Email Form</a>
        """

@app.route('/test_admin_email')
def test_admin_email():
    """Quick test endpoint to send a test email to admin via Hostinger"""
    try:
        # Send a test email to admin via Hostinger
        test_subject = "🧪 Test Admin Notification from GigGenius"
        test_message = "This is a test admin notification to verify that the Hostinger email functionality is working correctly! If you receive this, your admin notification system is working perfectly."

        result = send_admin_notification(test_subject, test_message, 'normal')

        if result['success']:
            return f"""
            <h2>✅ Admin Test Email Sent Successfully!</h2>
            <p>A test admin notification has been sent to: <strong>{ADMIN_EMAIL}</strong></p>
            <p>Check the admin inbox to confirm the email was received.</p>
            <br>
            <a href="/">Go to Home</a>
            """
        else:
            return f"""
            <h2>❌ Admin Test Email Failed</h2>
            <p>Error: {result['error']}</p>
            <p>Please check your Hostinger email configuration.</p>
            <br>
            <a href="/">Go to Home</a>
            """

    except Exception as e:
        return f"""
        <h2>❌ Admin Test Email Error</h2>
        <p>Exception: {str(e)}</p>
        <p>Please check your Hostinger email configuration and try again.</p>
        <br>
        <a href="/">Go to Home</a>
        """

@app.route('/health')
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'email_configured': EMAIL_CONFIG['email'] != '<EMAIL>',
        'sender_email': EMAIL_CONFIG['email'],
        'admin_recipient': ADMIN_EMAIL,
        'test_recipient': TEST_EMAIL,
        'total_users': len(users_db),
        'total_notifications': len(admin_notifications)
    })

@app.route('/test_auth')
def test_auth():
    """Test email authentication"""
    try:
        print("🔐 Testing email authentication...")

        # Test SMTP connection and authentication
        server = smtplib.SMTP(EMAIL_CONFIG['smtp_server'], EMAIL_CONFIG['smtp_port'], timeout=10)
        server.set_debuglevel(1)  # Show detailed SMTP conversation

        if EMAIL_CONFIG['use_tls']:
            server.starttls()

        # Try to login
        server.login(EMAIL_CONFIG['email'], EMAIL_CONFIG['password'])
        server.quit()

        return """
        <h2>✅ Email Authentication Successful!</h2>
        <p>Email authentication is working correctly.</p>
        <p>You can now send <NAME_EMAIL></p>
        <br>
        <a href="/test_email">Send Test Email</a> |
        <a href="/test_admin_email">Send Test Admin Email</a> |
        <a href="/email_form">Email Form</a>
        """

    except smtplib.SMTPAuthenticationError as e:
        return f"""
        <h2>❌ Email Authentication Failed</h2>
        <p><strong>Error:</strong> {str(e)}</p>
        <p><strong>Email:</strong> {EMAIL_CONFIG['email']}</p>
        <p><strong>Password Length:</strong> {len(EMAIL_CONFIG['password'])} characters</p>

        <h3>🔧 Troubleshooting:</h3>
        <ol>
            <li><strong>Check your Hostinger email credentials</strong></li>
            <li><strong>Make sure the email account exists</strong> in your Hostinger panel</li>
            <li><strong>Verify SMTP settings</strong> in Hostinger documentation</li>
            <li><strong>Check if SMTP is enabled</strong> for your email account</li>
        </ol>

        <br>
        <a href="/test_config">Full Config Test</a>
        """

    except Exception as e:
        return f"""
        <h2>❌ Email Connection Error</h2>
        <p><strong>Error:</strong> {str(e)}</p>
        <br>
        <a href="/test_config">Full Config Test</a>
        """

# Alias route for backward compatibility
@app.route('/test_hostinger_auth')
def test_hostinger_auth():
    """Alias for test_auth - Test Hostinger authentication"""
    return test_auth()

@app.route('/test_config')
def test_config():
    """Test Hostinger email configuration without sending email"""
    try:
        # Check Hostinger configuration
        config_issues = []

        if not EMAIL_CONFIG['email']:
            config_issues.append("❌ Email address not configured")
        else:
            config_issues.append(f"✅ Email: {EMAIL_CONFIG['email']}")

        if not EMAIL_CONFIG['password'] or EMAIL_CONFIG['password'] == 'your-hostinger-password':
            config_issues.append("❌ Email password not configured")
        else:
            config_issues.append(f"✅ Password: {'*' * len(EMAIL_CONFIG['password'])} (length: {len(EMAIL_CONFIG['password'])})")

        config_issues.append(f"✅ SMTP Server: {EMAIL_CONFIG['smtp_server']}:{EMAIL_CONFIG['smtp_port']}")
        config_issues.append(f"✅ TLS Enabled: {EMAIL_CONFIG['use_tls']}")
        config_issues.append(f"✅ Admin Recipient: {ADMIN_EMAIL}")
        config_issues.append(f"✅ Test Recipient: {TEST_EMAIL}")

        # Test SMTP connection
        try:
            print("🔌 Testing SMTP connection...")
            server = smtplib.SMTP(EMAIL_CONFIG['smtp_server'], EMAIL_CONFIG['smtp_port'], timeout=10)
            if EMAIL_CONFIG['use_tls']:
                server.starttls()
            config_issues.append("✅ SMTP connection successful")

            # Test authentication if credentials are provided
            if EMAIL_CONFIG['password'] != 'your-hostinger-password':
                try:
                    server.login(EMAIL_CONFIG['email'], EMAIL_CONFIG['password'])
                    config_issues.append("✅ Authentication successful")
                    server.quit()
                except Exception as auth_error:
                    config_issues.append(f"❌ Authentication failed: {str(auth_error)}")
            else:
                config_issues.append("⚠️ Skipping authentication test (credentials not configured)")
        except Exception as conn_error:
            config_issues.append(f"❌ SMTP connection failed: {str(conn_error)}")

        return f"""
        <h2>📧 Hostinger Email Configuration Test</h2>

        <div style="font-family: monospace; background: #f5f5f5; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
            {'<br>'.join(config_issues)}
        </div>

        <h3>� Setup Instructions:</h3>
        <ol>
            <li><strong>Create email account</strong> in Hostinger control panel</li>
            <li><strong>Enable SMTP</strong> for the email account</li>
            <li><strong>Use your email password</strong> (not an app password)</li>
            <li><strong>Update sample1.py:</strong>
                <pre style="background: #e8e8e8; padding: 10px; border-radius: 4px;">
EMAIL_CONFIG = {{
    'email': '<EMAIL>',
    'password': 'your-actual-hostinger-password',
    ...
}}
                </pre>
            </li>
        </ol>

        <h3>📧 Email Flow:</h3>
        <ul>
            <li><strong>Sender:</strong> {EMAIL_CONFIG['email']}</li>
            <li><strong>Admin Notifications:</strong> → {ADMIN_EMAIL}</li>
            <li><strong>User Notifications:</strong> → User's email address</li>
            <li><strong>Test Emails:</strong> → {TEST_EMAIL}</li>
        </ul>

        <br>
        <a href="/">← Back to Home</a> |
        <a href="/test_auth">Test Authentication</a> |
        <a href="/test_admin_email">Test Admin Email</a>
        """

    except Exception as e:
        return f"""
        <h2>❌ Configuration Test Error</h2>
        <p>Error: {str(e)}</p>
        <br>
        <a href="/">← Back to Home</a>
        """

if __name__ == '__main__':
    print("🚀 Starting GigGenius Simple Email System...")
    print("\n📧 Email Configuration:")
    print(f"   SMTP Server: {EMAIL_CONFIG['smtp_server']}")
    print(f"   SMTP Port: {EMAIL_CONFIG['smtp_port']}")
    print(f"   Sender Email: {EMAIL_CONFIG['email']}")
    print(f"   TLS Enabled: {EMAIL_CONFIG['use_tls']}")

    print("\n🎯 Email Flow:")
    print(f"   📤 All emails sent FROM: {EMAIL_CONFIG['email']}")
    print(f"   📧 Admin notifications TO: {ADMIN_EMAIL}")
    print(f"   📧 User notifications TO: User's email address")
    print(f"   📧 Test emails TO: {TEST_EMAIL}")

    print("\n📍 Available Routes:")
    print("   / - Main navigation page")
    print("   /register_demo - User registration demo")
    print("   /admin_panel - Admin notifications panel")
    print("   /email_form - Email form interface")
    print("   /test_email - Test email to your address")
    print("   /test_admin_email - Test admin notification")
    print("   /test_auth - Test email authentication")
    print("   /test_config - Test email configuration")
    print("   /health - Health check")

    print("\n⚠️  Configuration:")
    print("   • Update EMAIL_CONFIG password with your actual Hostinger password")
    print("   • Simple system: <EMAIL> sends all emails")
    print("   • User registers → User gets email + Admin gets notified")

    print("\n🌐 Starting server on http://localhost:5000")

    app.run(debug=True, host='0.0.0.0', port=5000)
