#!/usr/bin/env python3
"""
Simple Admin Email Test for GigGenius
"""

import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart

# Email config
EMAIL_CONFIG = {
    'smtp_server': 'smtp.hostinger.com',
    'smtp_port': 465,
    'email': '<EMAIL>',
    'password': 'Happiness1524$',
    'use_ssl': True
}

def send_simple_admin_test():
    """Send a simple admin test email to Gmail only"""
    try:
        print("🧪 Testing Admin Email to Gmail...")
        
        # Create message
        msg = MIMEMultipart()
        msg['From'] = EMAIL_CONFIG['email']
        msg['To'] = '<EMAIL>'
        msg['Subject'] = '🔔 SIMPLE TEST: Admin Notification Fixed'
        
        body = """
        <html>
        <body style="font-family: Arial, sans-serif;">
            <h2 style="color: #e91e63;">✅ Admin Email System Fixed!</h2>
            <p>This email confirms that admin notifications are now working properly.</p>
            <div style="background-color: #d4edda; padding: 15px; border-radius: 8px;">
                <p><strong>✅ Solution:</strong> Admin notifications now go directly to your Gmail instead of trying to <NAME_EMAIL> to <EMAIL></p>
            </div>
            <p>You should now receive all admin notifications reliably in your Gmail inbox!</p>
            <hr>
            <p><em>GigGenius Email System Test</em></p>
        </body>
        </html>
        """
        
        msg.attach(MIMEText(body, 'html'))
        
        # Send email
        print("🔌 Connecting to SMTP server...")
        server = smtplib.SMTP_SSL(EMAIL_CONFIG['smtp_server'], EMAIL_CONFIG['smtp_port'], timeout=30)
        
        print("🔑 Logging in...")
        server.login(EMAIL_CONFIG['email'], EMAIL_CONFIG['password'])
        
        print("📤 Sending admin test email...")
        text = msg.as_string()
        server.sendmail(EMAIL_CONFIG['email'], '<EMAIL>', text)
        
        print("✅ Admin test email sent successfully!")
        server.quit()
        
        return True
        
    except Exception as e:
        print(f"❌ Admin test failed: {str(e)}")
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("🧪 Simple Admin Email Test")
    print("=" * 50)
    
    success = send_simple_admin_test()
    
    if success:
        print("\n🎉 SUCCESS!")
        print("📧 Check your Gmail for the admin test email.")
        print("✅ Admin notifications are now fixed!")
    else:
        print("\n❌ FAILED!")
        print("🔧 Please check the configuration.")
    
    print("=" * 50)
