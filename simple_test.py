#!/usr/bin/env python3
"""
Simple test to check database connection and approve a genius
"""

import mysql.connector

# Database configuration
DB_CONFIG = {
    'host': '**************',
    'user': 'giggenius_user',
    'password': 'Happiness1524!',
    'database': 'giggenius',
    'port': 3306,
    'connect_timeout': 10,
    'use_pure': True,
    'autocommit': False,
    'buffered': True,
    'consume_results': True
}

def simple_approve_test():
    """Simple test to approve a genius"""
    try:
        print("🔗 Connecting to database...")
        conn = mysql.connector.connect(**DB_CONFIG)
        cursor = conn.cursor(dictionary=True)
        print("✅ Connected successfully!")
        
        # Get one pending genius with all required fields
        print("🔍 Finding a pending genius...")
        cursor.execute("""
            SELECT id, first_name, last_name, email, password, birthday, country, mobile,
                   position, expertise, hourly_rate, availability, tax_id, introduction,
                   professional_sum, terms_agreement, created_at
            FROM register_genius LIMIT 1
        """)
        genius = cursor.fetchone()

        if not genius:
            print("❌ No genius found")
            return

        print(f"✅ Found genius: {genius['first_name']} {genius['last_name']} (ID: {genius['id']})")

        # Insert with all required fields
        print("🔄 Attempting approval with all required fields...")
        cursor.execute("""
            INSERT INTO approve_genius (
                first_name, last_name, email, password, birthday, country, mobile,
                position, expertise, hourly_rate, availability, tax_id, introduction,
                professional_sum, terms_agreement, status, created_at, updated_at
            ) VALUES (
                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, 'approved', %s, NOW()
            )
        """, (
            genius['first_name'], genius['last_name'], genius['email'], genius['password'],
            genius['birthday'], genius['country'], genius['mobile'], genius['position'],
            genius['expertise'], genius['hourly_rate'], genius['availability'], genius['tax_id'],
            genius['introduction'], genius['professional_sum'], genius['terms_agreement'],
            genius['created_at']
        ))
        
        print("✅ Insert successful!")
        
        # Delete from register_genius
        cursor.execute("DELETE FROM register_genius WHERE id = %s", (genius['id'],))
        print("✅ Delete successful!")
        
        conn.commit()
        print("🎉 Approval completed successfully!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        try:
            conn.rollback()
        except:
            pass
    finally:
        try:
            cursor.close()
            conn.close()
        except:
            pass

if __name__ == "__main__":
    simple_approve_test()
