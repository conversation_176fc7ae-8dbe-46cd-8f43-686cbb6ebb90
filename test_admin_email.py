#!/usr/bin/env python3
"""
Test Admin Email Notifications for GigGenius
This script tests if admin notifications are being sent <NAME_EMAIL>
"""

import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON><PERSON>ipart
import sys

# 📧 EMAIL CONFIGURATION - Updated for Hostinger
EMAIL_CONFIG = {
    'smtp_server': 'smtp.hostinger.com',
    'smtp_port': 465,  # Use 465 for SSL instead of 587 for TLS
    'email': '<EMAIL>',  # This sends all emails
    'password': 'Happiness1524$',  # Your Hostinger password
    'use_ssl': True,  # Use SSL instead of TLS
    'use_tls': False
}

ADMIN_EMAIL = '<EMAIL>'

def test_admin_notification():
    """Test sending admin notification email"""
    try:
        print("🧪 Testing Admin Notification Email...")
        print(f"📧 From: {EMAIL_CONFIG['email']}")
        print(f"📧 To: {ADMIN_EMAIL}")
        
        # Create admin notification message
        msg = MIMEMultipart()
        msg['From'] = EMAIL_CONFIG['email']
        msg['To'] = ADMIN_EMAIL
        msg['Subject'] = "🔔 TEST: New Genius Registration - John Doe"
        
        # Admin notification body (similar to what's sent during registration)
        body = """
        <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                <h2 style="color: #e91e63;">🔔 New Genius Registration</h2>
                
                <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
                    <h3 style="color: #2196f3; margin-top: 0;">Genius Details:</h3>
                    <p><strong>Name:</strong> John Doe (TEST)</p>
                    <p><strong>Email:</strong> <EMAIL></p>
                    <p><strong>Country:</strong> Philippines</p>
                    <p><strong>Position:</strong> Web Developer</p>
                    <p><strong>Expertise:</strong> Full Stack Development</p>
                    <p><strong>Hourly Rate:</strong> $25/hour</p>
                    <p><strong>Availability:</strong> Full-time</p>
                </div>
                
                <div style="background-color: #fff3cd; padding: 15px; border-radius: 8px; border-left: 4px solid #ffc107;">
                    <p><strong>⚠️ Action Required:</strong> Please review and approve this genius registration in the admin panel.</p>
                </div>
                
                <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
                <p style="color: #666; font-size: 14px;">
                    <em>This is an automated notification from the GigGenius registration system.</em><br>
                    <strong>Test Email - Admin Notification System</strong>
                </p>
            </div>
        </body>
        </html>
        """
        
        msg.attach(MIMEText(body, 'html'))
        
        # Create SMTP session
        print("🔌 Connecting to SMTP server...")
        
        if EMAIL_CONFIG.get('use_ssl', False):
            server = smtplib.SMTP_SSL(EMAIL_CONFIG['smtp_server'], EMAIL_CONFIG['smtp_port'], timeout=30)
            print("🔐 Using SSL connection...")
        else:
            server = smtplib.SMTP(EMAIL_CONFIG['smtp_server'], EMAIL_CONFIG['smtp_port'], timeout=30)
            print("🔐 Starting TLS...")
            if EMAIL_CONFIG.get('use_tls', True):
                server.starttls()
        
        # Enable debug output
        server.set_debuglevel(1)
        
        print("🔑 Logging in...")
        server.login(EMAIL_CONFIG['email'], EMAIL_CONFIG['password'])
        
        print("📤 Sending admin notification email...")
        text = msg.as_string()
        server.sendmail(EMAIL_CONFIG['email'], ADMIN_EMAIL, text)
        
        print("✅ Admin notification sent successfully!")
        server.quit()
        
        return True
        
    except Exception as e:
        print(f"❌ Admin notification test failed: {str(e)}")
        return False

def test_user_notification():
    """Test sending user notification email to a Gmail address"""
    try:
        print("\n🧪 Testing User Notification Email...")
        test_gmail = "<EMAIL>"  # You can change this to your Gmail
        print(f"📧 From: {EMAIL_CONFIG['email']}")
        print(f"📧 To: {test_gmail}")
        
        # Create user welcome message
        msg = MIMEMultipart()
        msg['From'] = EMAIL_CONFIG['email']
        msg['To'] = test_gmail
        msg['Subject'] = "Welcome to GigGenius - Your Genius Registration (TEST)"
        
        # User welcome body
        body = """
        <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                <h2 style="color: #e91e63;">🎉 Welcome to GigGenius!</h2>
                
                <p>Dear John,</p>
                
                <p>Thank you for registering as a Genius on our platform! We're excited to have you join our community of talented professionals.</p>
                
                <div style="background-color: #e3f2fd; padding: 20px; border-radius: 8px; margin: 20px 0;">
                    <h3 style="color: #2196f3; margin-top: 0;">What's Next?</h3>
                    <ul>
                        <li>Your registration is being reviewed by our team</li>
                        <li>You'll receive an approval notification within 24-48 hours</li>
                        <li>Once approved, you can start browsing and applying for jobs</li>
                    </ul>
                </div>
                
                <p>If you have any questions, feel free to contact our support team.</p>
                
                <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
                <p style="color: #666; font-size: 14px;">
                    <em>This is a test email from the GigGenius registration system.</em><br>
                    <strong>Best regards,<br>The GigGenius Team</strong>
                </p>
            </div>
        </body>
        </html>
        """
        
        msg.attach(MIMEText(body, 'html'))
        
        # Create SMTP session
        print("🔌 Connecting to SMTP server...")
        
        if EMAIL_CONFIG.get('use_ssl', False):
            server = smtplib.SMTP_SSL(EMAIL_CONFIG['smtp_server'], EMAIL_CONFIG['smtp_port'], timeout=30)
            print("🔐 Using SSL connection...")
        else:
            server = smtplib.SMTP(EMAIL_CONFIG['smtp_server'], EMAIL_CONFIG['smtp_port'], timeout=30)
            print("🔐 Starting TLS...")
            if EMAIL_CONFIG.get('use_tls', True):
                server.starttls()
        
        print("🔑 Logging in...")
        server.login(EMAIL_CONFIG['email'], EMAIL_CONFIG['password'])
        
        print("📤 Sending user notification email...")
        text = msg.as_string()
        server.sendmail(EMAIL_CONFIG['email'], test_gmail, text)
        
        print("✅ User notification sent successfully!")
        server.quit()
        
        return True
        
    except Exception as e:
        print(f"❌ User notification test failed: {str(e)}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("🧪 GigGenius Registration Email Test")
    print("=" * 60)
    
    # Test admin notification
    admin_success = test_admin_notification()
    
    # Test user notification
    user_success = test_user_notification()
    
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS:")
    print(f"✅ Admin Notification: {'SUCCESS' if admin_success else 'FAILED'}")
    print(f"✅ User Notification: {'SUCCESS' if user_success else 'FAILED'}")
    
    if admin_success and user_success:
        print("\n🎉 ALL TESTS PASSED!")
        print("📧 Check both your Hostinger inbox and Gmail for test emails.")
    else:
        print("\n❌ SOME TESTS FAILED!")
        print("🔧 Please check the email configuration and try again.")
    
    print("=" * 60)
