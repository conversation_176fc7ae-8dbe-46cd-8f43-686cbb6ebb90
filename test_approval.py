#!/usr/bin/env python3
"""
Test script to manually approve a genius
"""

import mysql.connector
from datetime import datetime

# Database configuration
DB_CONFIG = {
    'host': '**************',
    'user': 'giggenius_user',
    'password': 'Happiness1524!',
    'database': 'giggenius',
    'port': 3306,
    'connect_timeout': 10,
    'use_pure': True,
    'autocommit': False,  # We want to control transactions
    'buffered': True,
    'consume_results': True
}

def approve_genius_manually(genius_id):
    """Manually approve a genius by moving them from register_genius to approve_genius"""
    try:
        # Create connection
        conn = mysql.connector.connect(**DB_CONFIG)
        cursor = conn.cursor(dictionary=True)
        
        print(f"🔍 Attempting to approve genius with ID: {genius_id}")
        
        # First, get the genius data from register_genius
        cursor.execute("""
            SELECT profile_photo, first_name, last_name, email, password,
                   birthday, country, mobile, position, expertise, hourly_rate,
                   availability, tax_id, introduction, professional_sum,
                   id_front, id_back, email_updates, terms_agreement,
                   is_pwd, pwd_condition, pwd_proof, commission_rate,
                   pwd_approval_status, pwd_approved_by, pwd_approved_at,
                   pwd_rejection_reason, created_at
            FROM register_genius
            WHERE id = %s
        """, (genius_id,))
        
        genius_data = cursor.fetchone()
        if not genius_data:
            print(f"❌ Genius with ID {genius_id} not found in register_genius table")
            return False
        
        print(f"✅ Found genius: {genius_data['first_name']} {genius_data['last_name']}")
        
        # Insert into approve_genius table with only the common columns
        print(f"🔄 Inserting into approve_genius table...")

        # Count parameters: 26 placeholders + 'approved' + NOW() = 28 total
        # But we have 27 values in the tuple, so we need to match exactly
        cursor.execute("""
            INSERT INTO approve_genius (
                profile_photo, first_name, last_name, email, password,
                birthday, country, mobile, position, expertise, hourly_rate,
                availability, tax_id, introduction, professional_sum,
                id_front, id_back, email_updates, terms_agreement,
                is_pwd, pwd_condition, pwd_proof, commission_rate,
                pwd_approval_status, pwd_approved_by, pwd_approved_at,
                pwd_rejection_reason, status, created_at, updated_at
            ) VALUES (
                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW()
            )
        """, (
            genius_data['profile_photo'], genius_data['first_name'], genius_data['last_name'],
            genius_data['email'], genius_data['password'], genius_data['birthday'],
            genius_data['country'], genius_data['mobile'], genius_data['position'],
            genius_data['expertise'], genius_data['hourly_rate'], genius_data['availability'],
            genius_data['tax_id'], genius_data['introduction'], genius_data['professional_sum'],
            genius_data['id_front'], genius_data['id_back'], genius_data['email_updates'],
            genius_data['terms_agreement'], genius_data['is_pwd'], genius_data['pwd_condition'],
            genius_data['pwd_proof'], genius_data['commission_rate'],
            genius_data['pwd_approval_status'], genius_data['pwd_approved_by'],
            genius_data['pwd_approved_at'], genius_data['pwd_rejection_reason'],
            'approved', genius_data['created_at']
        ))
        
        print(f"✅ Successfully inserted into approve_genius table")
        
        # Delete from register_genius table
        print(f"🗑️ Deleting from register_genius table...")
        cursor.execute("DELETE FROM register_genius WHERE id = %s", (genius_id,))
        print(f"✅ Successfully deleted from register_genius table")
        
        # Commit the transaction
        conn.commit()
        print(f"🎉 Successfully approved genius {genius_data['first_name']} {genius_data['last_name']}!")
        
        return True
        
    except Exception as e:
        print(f"❌ Error approving genius: {e}")
        import traceback
        traceback.print_exc()
        try:
            conn.rollback()
        except:
            pass
        return False
    finally:
        try:
            cursor.close()
            conn.close()
        except:
            pass

def list_pending_geniuses():
    """List all pending geniuses"""
    try:
        conn = mysql.connector.connect(**DB_CONFIG)
        cursor = conn.cursor(dictionary=True)
        
        cursor.execute("""
            SELECT id, first_name, last_name, email, country, status 
            FROM register_genius 
            WHERE status = 'pending' OR status IS NULL
            ORDER BY created_at DESC
        """)
        
        geniuses = cursor.fetchall()
        
        print("📋 Pending geniuses:")
        for genius in geniuses:
            print(f"  - ID: {genius['id']}, Name: {genius['first_name']} {genius['last_name']}, Email: {genius['email']}, Country: {genius['country']}")
        
        return geniuses
        
    except Exception as e:
        print(f"❌ Error listing geniuses: {e}")
        return []
    finally:
        try:
            cursor.close()
            conn.close()
        except:
            pass

if __name__ == "__main__":
    print("🔍 Listing pending geniuses...")
    pending_geniuses = list_pending_geniuses()
    
    if pending_geniuses:
        print(f"\n🎯 Let's approve the first genius (ID: {pending_geniuses[0]['id']})")
        success = approve_genius_manually(pending_geniuses[0]['id'])
        
        if success:
            print("\n🔍 Checking results...")
            list_pending_geniuses()
    else:
        print("No pending geniuses found.")
