#!/usr/bin/env python3
"""
Test script to check database table structures
"""

import mysql.connector
from mysql.connector import pooling

# Database configuration
DB_CONFIG = {
    'host': '**************',
    'user': 'giggenius_user',
    'password': 'Happiness1524!',
    'database': 'giggenius',
    'port': 3306,
    'connect_timeout': 10,
    'use_pure': True,
    'autocommit': True,
    'buffered': True,
    'consume_results': True
}

def test_table_structures():
    """Test and compare table structures"""
    try:
        # Create connection
        conn = mysql.connector.connect(**DB_CONFIG)
        cursor = conn.cursor(dictionary=True)
        
        print("🔍 Checking register_genius table structure...")
        cursor.execute("DESCRIBE register_genius")
        register_columns = cursor.fetchall()
        
        print("\n📋 register_genius columns:")
        for col in register_columns:
            print(f"  - {col['Field']} ({col['Type']})")
        
        print("\n🔍 Checking approve_genius table structure...")
        cursor.execute("DESCRIBE approve_genius")
        approve_columns = cursor.fetchall()
        
        print("\n📋 approve_genius columns:")
        for col in approve_columns:
            print(f"  - {col['Field']} ({col['Type']})")
        
        # Check for missing columns
        register_fields = {col['Field'] for col in register_columns}
        approve_fields = {col['Field'] for col in approve_columns}
        
        missing_in_approve = register_fields - approve_fields
        missing_in_register = approve_fields - register_fields
        
        if missing_in_approve:
            print(f"\n❌ Columns missing in approve_genius: {missing_in_approve}")
        
        if missing_in_register:
            print(f"\n❌ Columns missing in register_genius: {missing_in_register}")
        
        if not missing_in_approve and not missing_in_register:
            print("\n✅ Both tables have matching column structures!")
        
        # Check sample data
        print("\n🔍 Checking sample data from register_genius...")
        cursor.execute("SELECT id, first_name, last_name, email, country FROM register_genius LIMIT 3")
        register_data = cursor.fetchall()
        
        print("📋 Sample register_genius data:")
        for row in register_data:
            print(f"  - ID: {row['id']}, Name: {row['first_name']} {row['last_name']}, Email: {row['email']}, Country: {row['country']}")
        
        print("\n🔍 Checking sample data from approve_genius...")
        cursor.execute("SELECT id, first_name, last_name, email, country FROM approve_genius LIMIT 3")
        approve_data = cursor.fetchall()
        
        print("📋 Sample approve_genius data:")
        for row in approve_data:
            print(f"  - ID: {row['id']}, Name: {row['first_name']} {row['last_name']}, Email: {row['email']}, Country: {row['country']}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        try:
            cursor.close()
            conn.close()
        except:
            pass

if __name__ == "__main__":
    test_table_structures()
