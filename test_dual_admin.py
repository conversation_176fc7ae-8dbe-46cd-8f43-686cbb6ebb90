#!/usr/bin/env python3
"""
Test Dual Admin Email Notifications for GigGenius
This script tests the updated admin notification system that sends to both emails
"""

import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import sys

# 📧 EMAIL CONFIGURATION - Updated for Hostinger
EMAIL_CONFIG = {
    'smtp_server': 'smtp.hostinger.com',
    'smtp_port': 465,  # Use 465 for SSL instead of 587 for TLS
    'email': '<EMAIL>',  # This sends all emails
    'password': 'Happiness1524$',  # Your Hostinger password
    'use_ssl': True,  # Use SSL instead of TLS
    'use_tls': False
}

ADMIN_EMAIL = '<EMAIL>'  # Primary admin email
BACKUP_ADMIN_EMAIL = '<EMAIL>'  # Backup admin email (your Gmail)

def send_email_notification(recipient_email, subject, message, priority='normal'):
    """Send email <NAME_EMAIL>"""
    try:
        print(f"📤 Sending email to: {recipient_email}")
        print(f"📧 Subject: {subject}")
        
        # Create message
        msg = MIMEMultipart()
        msg['From'] = EMAIL_CONFIG['email']
        msg['To'] = recipient_email
        msg['Subject'] = subject
        
        msg.attach(MIMEText(message, 'html'))
        
        # Create SMTP session
        if EMAIL_CONFIG.get('use_ssl', False):
            server = smtplib.SMTP_SSL(EMAIL_CONFIG['smtp_server'], EMAIL_CONFIG['smtp_port'], timeout=30)
        else:
            server = smtplib.SMTP(EMAIL_CONFIG['smtp_server'], EMAIL_CONFIG['smtp_port'], timeout=30)
            if EMAIL_CONFIG.get('use_tls', True):
                server.starttls()
        
        server.login(EMAIL_CONFIG['email'], EMAIL_CONFIG['password'])
        text = msg.as_string()
        server.sendmail(EMAIL_CONFIG['email'], recipient_email, text)
        server.quit()
        
        print(f"✅ Email sent successfully to {recipient_email}")
        return {'success': True, 'message': 'Email sent successfully'}
        
    except Exception as e:
        print(f"❌ Failed to send email to {recipient_email}: {str(e)}")
        return {'success': False, 'error': str(e)}

def send_admin_notification(subject, message, priority='normal'):
    """
    Send admin notification to both primary and backup admin emails
    """
    print("🔔 Sending dual admin notifications...")
    results = []
    
    # Send to primary admin email (Hostinger)
    print("\n📧 Sending to PRIMARY admin email (Hostinger)...")
    primary_result = send_email_notification(
        recipient_email=ADMIN_EMAIL,
        subject=f"[PRIMARY] {subject}",
        message=message,
        priority=priority
    )
    results.append(('Primary Admin', primary_result))
    
    # Send to backup admin email (Gmail)
    print("\n📧 Sending to BACKUP admin email (Gmail)...")
    backup_result = send_email_notification(
        recipient_email=BACKUP_ADMIN_EMAIL,
        subject=f"[BACKUP] {subject}",
        message=message,
        priority=priority
    )
    results.append(('Backup Admin', backup_result))
    
    # Return combined result
    success_count = sum(1 for _, result in results if result['success'])
    total_count = len(results)
    
    return {
        'success': success_count > 0,  # Success if at least one email sent
        'message': f'Admin notifications: {success_count}/{total_count} sent successfully',
        'details': results
    }

def test_dual_admin_notification():
    """Test the dual admin notification system"""
    try:
        print("🧪 Testing Dual Admin Notification System...")
        
        # Create test admin notification
        subject = "🔔 TEST: New Genius Registration - Jane Smith"
        message = """
        <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                <h2 style="color: #e91e63;">🔔 New Genius Registration</h2>
                
                <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
                    <h3 style="color: #2196f3; margin-top: 0;">Genius Details:</h3>
                    <p><strong>Name:</strong> Jane Smith (DUAL TEST)</p>
                    <p><strong>Email:</strong> <EMAIL></p>
                    <p><strong>Country:</strong> Philippines</p>
                    <p><strong>Position:</strong> UI/UX Designer</p>
                    <p><strong>Expertise:</strong> Web Design & User Experience</p>
                    <p><strong>Hourly Rate:</strong> $30/hour</p>
                    <p><strong>Availability:</strong> Part-time</p>
                </div>
                
                <div style="background-color: #fff3cd; padding: 15px; border-radius: 8px; border-left: 4px solid #ffc107;">
                    <p><strong>⚠️ Action Required:</strong> Please review and approve this genius registration in the admin panel.</p>
                </div>
                
                <div style="background-color: #d1ecf1; padding: 15px; border-radius: 8px; border-left: 4px solid #17a2b8; margin-top: 20px;">
                    <p><strong>📧 Dual Email Test:</strong> This notification is being sent to BOTH your Hostinger and Gmail accounts to ensure you receive admin notifications.</p>
                </div>
                
                <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
                <p style="color: #666; font-size: 14px;">
                    <em>This is a test of the dual admin notification system.</em><br>
                    <strong>GigGenius Registration System</strong>
                </p>
            </div>
        </body>
        </html>
        """
        
        # Send dual admin notification
        result = send_admin_notification(subject, message, 'high')
        
        print(f"\n📊 DUAL ADMIN NOTIFICATION RESULT:")
        print(f"✅ Overall Success: {result['success']}")
        print(f"📝 Message: {result['message']}")
        
        for email_type, email_result in result['details']:
            status = "✅ SUCCESS" if email_result['success'] else "❌ FAILED"
            print(f"   {email_type}: {status}")
            if not email_result['success']:
                print(f"      Error: {email_result.get('error', 'Unknown error')}")
        
        return result['success']
        
    except Exception as e:
        print(f"❌ Dual admin notification test failed: {str(e)}")
        return False

if __name__ == "__main__":
    print("=" * 70)
    print("🧪 GigGenius Dual Admin Email Notification Test")
    print("=" * 70)
    
    success = test_dual_admin_notification()
    
    print("\n" + "=" * 70)
    if success:
        print("🎉 DUAL ADMIN NOTIFICATION TEST PASSED!")
        print("📧 Check BOTH your Hostinger inbox AND Gmail for admin notifications.")
        print("✅ You should now receive admin notifications in both places!")
    else:
        print("❌ DUAL ADMIN NOTIFICATION TEST FAILED!")
        print("🔧 Please check the email configuration and try again.")
    
    print("=" * 70)
