#!/usr/bin/env python3
"""
Test Email Configuration for GigGenius
This script tests the email functionality with the updated Hostinger settings
"""

import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import sys

# 📧 EMAIL CONFIGURATION - Updated for Hostinger
EMAIL_CONFIG = {
    'smtp_server': 'smtp.hostinger.com',
    'smtp_port': 465,  # Use 465 for SSL instead of 587 for TLS
    'email': '<EMAIL>',  # This sends all emails
    'password': 'Happiness1524$',  # Your Hostinger password
    'use_ssl': True,  # Use SSL instead of TLS
    'use_tls': False
}

def test_email_connection():
    """Test the email connection and send a test email"""
    try:
        print("🧪 Testing Email Configuration...")
        print(f"📧 SMTP Server: {EMAIL_CONFIG['smtp_server']}")
        print(f"🔌 Port: {EMAIL_CONFIG['smtp_port']}")
        print(f"👤 Email: {EMAIL_CONFIG['email']}")
        print(f"🔐 SSL: {EMAIL_CONFIG['use_ssl']}")
        
        # Create test message
        msg = MIMEMultipart()
        msg['From'] = EMAIL_CONFIG['email']
        msg['To'] = EMAIL_CONFIG['email']  # Send to self for testing
        msg['Subject'] = "🧪 GigGenius Email Test - Configuration Working!"
        
        # Email body
        body = """
        <html>
        <body>
            <h2>✅ Email Configuration Test Successful!</h2>
            <p>This is a test email to verify that the GigGenius email system is working correctly.</p>
            <p><strong>Configuration Details:</strong></p>
            <ul>
                <li>SMTP Server: smtp.hostinger.com</li>
                <li>Port: 465 (SSL)</li>
                <li>From: <EMAIL></li>
            </ul>
            <p>If you receive this email, the configuration is working properly! 🎉</p>
            <hr>
            <p><em>GigGenius Email System Test</em></p>
        </body>
        </html>
        """
        
        msg.attach(MIMEText(body, 'html'))
        
        # Create SMTP session
        print("🔌 Connecting to SMTP server...")
        
        if EMAIL_CONFIG.get('use_ssl', False):
            # Use SMTP_SSL for port 465
            server = smtplib.SMTP_SSL(EMAIL_CONFIG['smtp_server'], EMAIL_CONFIG['smtp_port'], timeout=30)
            print("🔐 Using SSL connection...")
        else:
            # Use regular SMTP for port 587 with TLS
            server = smtplib.SMTP(EMAIL_CONFIG['smtp_server'], EMAIL_CONFIG['smtp_port'], timeout=30)
            print("🔐 Starting TLS...")
            if EMAIL_CONFIG.get('use_tls', True):
                server.starttls()  # Enable TLS encryption
        
        # Enable debug output
        server.set_debuglevel(1)
        
        print("🔑 Logging in...")
        server.login(EMAIL_CONFIG['email'], EMAIL_CONFIG['password'])
        
        print("📤 Sending test email...")
        text = msg.as_string()
        server.sendmail(EMAIL_CONFIG['email'], EMAIL_CONFIG['email'], text)
        
        print("✅ Test email sent successfully!")
        server.quit()
        
        return True
        
    except Exception as e:
        print(f"❌ Email test failed: {str(e)}")
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("🧪 GigGenius Email Configuration Test")
    print("=" * 50)
    
    success = test_email_connection()
    
    if success:
        print("\n🎉 SUCCESS! Email configuration is working!")
        print("✅ You should receive a test email shortly.")
        print("✅ The email system is ready for production use.")
    else:
        print("\n❌ FAILED! Email configuration needs adjustment.")
        print("🔧 Please check the SMTP settings and credentials.")
    
    print("=" * 50)
