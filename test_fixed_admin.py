#!/usr/bin/env python3
"""
Test Fixed Admin Email Notifications for GigGenius
This script tests the updated admin notification system that avoids self-sending issues
"""

import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import sys

# 📧 EMAIL CONFIGURATION - Updated for Hostinger
EMAIL_CONFIG = {
    'smtp_server': 'smtp.hostinger.com',
    'smtp_port': 465,  # Use 465 for SSL instead of 587 for TLS
    'email': '<EMAIL>',  # This sends all emails
    'password': 'Happiness1524$',  # Your Hostinger password
    'use_ssl': True,  # Use SSL instead of TLS
    'use_tls': False
}

ADMIN_EMAIL = '<EMAIL>'  # Primary admin email (your Gmail - more reliable)
BACKUP_ADMIN_EMAIL = '<EMAIL>'  # Backup admin email (Hostinger - may go to spam)

def send_email_notification(recipient_email, subject, message, priority='normal'):
    """Send email <NAME_EMAIL>"""
    try:
        print(f"📤 Sending email to: {recipient_email}")
        print(f"📧 Subject: {subject}")
        
        # Create message
        msg = MIMEMultipart()
        msg['From'] = EMAIL_CONFIG['email']
        msg['To'] = recipient_email
        msg['Subject'] = subject
        
        msg.attach(MIMEText(message, 'html'))
        
        # Create SMTP session
        if EMAIL_CONFIG.get('use_ssl', False):
            server = smtplib.SMTP_SSL(EMAIL_CONFIG['smtp_server'], EMAIL_CONFIG['smtp_port'], timeout=30)
        else:
            server = smtplib.SMTP(EMAIL_CONFIG['smtp_server'], EMAIL_CONFIG['smtp_port'], timeout=30)
            if EMAIL_CONFIG.get('use_tls', True):
                server.starttls()
        
        server.login(EMAIL_CONFIG['email'], EMAIL_CONFIG['password'])
        text = msg.as_string()
        server.sendmail(EMAIL_CONFIG['email'], recipient_email, text)
        server.quit()
        
        print(f"✅ Email sent successfully to {recipient_email}")
        return {'success': True, 'message': 'Email sent successfully'}
        
    except Exception as e:
        print(f"❌ Failed to send email to {recipient_email}: {str(e)}")
        return {'success': False, 'error': str(e)}

def send_admin_notification(subject, message, priority='normal'):
    """
    Send admin notification to Gmail (primary) and optionally to Hostinger (backup)
    
    Note: Hostinger may reject self-sending emails, so Gmail is more reliable for admin notifications
    """
    print("🔔 Sending fixed admin notifications...")
    results = []
    
    # Send to primary admin email (Gmail - more reliable)
    print("\n📧 Sending to PRIMARY admin email (Gmail)...")
    primary_result = send_email_notification(
        recipient_email=ADMIN_EMAIL,
        subject=f"🔔 {subject}",
        message=message,
        priority=priority
    )
    results.append(('Primary Admin (Gmail)', primary_result))
    
    # Only send to backup if primary succeeds (to avoid spam)
    # Skip backup if it's the same as sender to avoid self-sending issues
    if BACKUP_ADMIN_EMAIL != EMAIL_CONFIG['email']:
        print("\n📧 Sending to BACKUP admin email (Hostinger)...")
        backup_result = send_email_notification(
            recipient_email=BACKUP_ADMIN_EMAIL,
            subject=f"[BACKUP] {subject}",
            message=message,
            priority=priority
        )
        results.append(('Backup Admin (Hostinger)', backup_result))
    else:
        print("\n⚠️ Skipping BACKUP email (same as sender - avoids self-sending issues)")
        results.append(('Backup Admin (Hostinger)', {'success': False, 'error': 'Skipped self-sending to avoid delivery issues'}))
    
    # Return combined result
    success_count = sum(1 for _, result in results if result['success'])
    total_count = len(results)
    
    return {
        'success': success_count > 0,  # Success if at least one email sent
        'message': f'Admin notifications: {success_count}/{total_count} sent successfully',
        'details': results
    }

def test_fixed_admin_notification():
    """Test the fixed admin notification system"""
    try:
        print("🧪 Testing Fixed Admin Notification System...")
        print(f"📧 Sender: {EMAIL_CONFIG['email']}")
        print(f"📧 Primary Admin: {ADMIN_EMAIL}")
        print(f"📧 Backup Admin: {BACKUP_ADMIN_EMAIL}")
        
        # Create test admin notification
        subject = "🔔 FIXED TEST: New Genius Registration - Alex Johnson"
        message = """
        <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                <h2 style="color: #e91e63;">🔔 New Genius Registration</h2>
                
                <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
                    <h3 style="color: #2196f3; margin-top: 0;">Genius Details:</h3>
                    <p><strong>Name:</strong> Alex Johnson (FIXED TEST)</p>
                    <p><strong>Email:</strong> <EMAIL></p>
                    <p><strong>Country:</strong> Philippines</p>
                    <p><strong>Position:</strong> Full Stack Developer</p>
                    <p><strong>Expertise:</strong> React, Node.js, Python</p>
                    <p><strong>Hourly Rate:</strong> $35/hour</p>
                    <p><strong>Availability:</strong> Full-time</p>
                </div>
                
                <div style="background-color: #d4edda; padding: 15px; border-radius: 8px; border-left: 4px solid #28a745;">
                    <p><strong>✅ Fixed Email System:</strong> This notification is now sent primarily to your Gmail to avoid delivery issues with self-sending emails.</p>
                </div>
                
                <div style="background-color: #fff3cd; padding: 15px; border-radius: 8px; border-left: 4px solid #ffc107;">
                    <p><strong>⚠️ Action Required:</strong> Please review and approve this genius registration in the admin panel.</p>
                </div>
                
                <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
                <p style="color: #666; font-size: 14px;">
                    <em>This is a test of the fixed admin notification system.</em><br>
                    <strong>GigGenius Registration System</strong>
                </p>
            </div>
        </body>
        </html>
        """
        
        # Send fixed admin notification
        result = send_admin_notification(subject, message, 'high')
        
        print(f"\n📊 FIXED ADMIN NOTIFICATION RESULT:")
        print(f"✅ Overall Success: {result['success']}")
        print(f"📝 Message: {result['message']}")
        
        for email_type, email_result in result['details']:
            status = "✅ SUCCESS" if email_result['success'] else "⚠️ SKIPPED" if 'Skipped' in email_result.get('error', '') else "❌ FAILED"
            print(f"   {email_type}: {status}")
            if not email_result['success']:
                print(f"      Reason: {email_result.get('error', 'Unknown error')}")
        
        return result['success']
        
    except Exception as e:
        print(f"❌ Fixed admin notification test failed: {str(e)}")
        return False

if __name__ == "__main__":
    print("=" * 70)
    print("🧪 GigGenius Fixed Admin Email Notification Test")
    print("=" * 70)
    
    success = test_fixed_admin_notification()
    
    print("\n" + "=" * 70)
    if success:
        print("🎉 FIXED ADMIN NOTIFICATION TEST PASSED!")
        print("📧 Check your Gmail for the admin notification.")
        print("✅ No more undelivered mail issues!")
        print("✅ Admin notifications now go directly to your Gmail!")
    else:
        print("❌ FIXED ADMIN NOTIFICATION TEST FAILED!")
        print("🔧 Please check the email configuration and try again.")
    
    print("=" * 70)
